"""Tests for site configuration and detection."""

import pytest
from ecommerce_scraper.config.sites import (
    get_site_config, 
    detect_site_type, 
    SiteType, 
    SiteConfig,
    get_extraction_strategy,
    get_navigation_strategy,
    SITE_CONFIGS
)


class TestSiteDetection:
    """Test site type detection from URLs."""
    
    def test_detect_amazon(self):
        """Test detecting Amazon sites."""
        amazon_urls = [
            "https://www.amazon.com/dp/B08N5WRWNW",
            "https://amazon.co.uk/product/123",
            "https://www.amazon.de/gp/product/456",
            "https://amazon.fr/dp/789",
        ]
        
        for url in amazon_urls:
            site_type = detect_site_type(url)
            assert site_type == SiteType.AMAZON
    
    def test_detect_ebay(self):
        """Test detecting eBay sites."""
        ebay_urls = [
            "https://www.ebay.com/itm/123456789",
            "https://ebay.co.uk/itm/987654321",
            "https://www.ebay.de/itm/555666777",
        ]
        
        for url in ebay_urls:
            site_type = detect_site_type(url)
            assert site_type == SiteType.EBAY
    
    def test_detect_shopify(self):
        """Test detecting Shopify stores."""
        shopify_urls = [
            "https://store.myshopify.com/products/item",
            "https://example-store.myshopify.com/collections/all",
            "https://custom-domain.com/products/item",  # This would be generic
        ]
        
        # Direct Shopify URLs
        assert detect_site_type(shopify_urls[0]) == SiteType.SHOPIFY
        assert detect_site_type(shopify_urls[1]) == SiteType.SHOPIFY
        
        # Custom domain would be detected as generic
        assert detect_site_type(shopify_urls[2]) == SiteType.GENERIC
    
    def test_detect_generic(self):
        """Test detecting generic ecommerce sites."""
        generic_urls = [
            "https://example-store.com/product/123",
            "https://shop.example.net/items/456",
            "https://unknown-ecommerce.org/products/789",
        ]
        
        for url in generic_urls:
            site_type = detect_site_type(url)
            assert site_type == SiteType.GENERIC


class TestSiteConfigs:
    """Test site configuration retrieval and properties."""
    
    def test_get_amazon_config(self):
        """Test getting Amazon configuration."""
        config = get_site_config("https://www.amazon.com/dp/B08N5WRWNW")
        
        assert config.name == "Amazon"
        assert config.site_type == SiteType.AMAZON
        assert config.base_url == "https://www.amazon.com"
        assert config.requires_cookie_consent is True
        assert config.has_location_selection is True
        assert config.delay_between_requests == 3
        
        # Check selectors
        assert "search_box" in config.selectors
        assert "product_title" in config.selectors
        assert "price_current" in config.selectors
        
        # Check instructions
        assert "handle_popups" in config.navigation_instructions
        assert "title" in config.extraction_instructions
    
    def test_get_ebay_config(self):
        """Test getting eBay configuration."""
        config = get_site_config("https://www.ebay.com/itm/123456789")
        
        assert config.name == "eBay"
        assert config.site_type == SiteType.EBAY
        assert config.base_url == "https://www.ebay.com"
        assert config.requires_cookie_consent is True
        assert config.delay_between_requests == 2
        
        # Check eBay-specific selectors
        assert "condition" in config.selectors
        assert "seller_info" in config.selectors
        
        # Check eBay-specific instructions
        assert "condition" in config.extraction_instructions
        assert "seller" in config.extraction_instructions
    
    def test_get_shopify_config(self):
        """Test getting Shopify configuration."""
        config = get_site_config("https://store.myshopify.com/products/item")
        
        assert config.name == "Shopify Store"
        assert config.site_type == SiteType.SHOPIFY
        assert config.base_url == "https://store.myshopify.com"
        assert config.requires_cookie_consent is True
        assert config.delay_between_requests == 2
        
        # Check Shopify-specific selectors
        assert "variants" in config.selectors
        assert "add_to_cart" in config.selectors
    
    def test_get_generic_config(self):
        """Test getting generic configuration."""
        config = get_site_config("https://unknown-store.com/product/123")
        
        assert config.name == "Generic Ecommerce"
        assert config.site_type == SiteType.GENERIC
        assert config.base_url == "https://unknown-store.com"
        assert config.requires_cookie_consent is True
        assert config.delay_between_requests == 2
        
        # Check generic instructions
        assert "adaptive" in config.extraction_instructions
        assert "comprehensive" in config.extraction_instructions


class TestExtractionStrategies:
    """Test extraction strategy configurations."""
    
    def test_amazon_extraction_strategy(self):
        """Test Amazon extraction strategy."""
        strategy = get_extraction_strategy(SiteType.AMAZON)
        
        assert strategy["approach"] == "structured_selectors"
        assert strategy["fallback"] == "ai_extraction"
        assert "title" in strategy["priority_fields"]
        assert "price" in strategy["priority_fields"]
        assert "variants" in strategy["special_handling"]
    
    def test_ebay_extraction_strategy(self):
        """Test eBay extraction strategy."""
        strategy = get_extraction_strategy(SiteType.EBAY)
        
        assert strategy["approach"] == "mixed_extraction"
        assert "condition" in strategy["priority_fields"]
        assert "seller" in strategy["priority_fields"]
        assert "auction_vs_buy_now" in strategy["special_handling"]
    
    def test_shopify_extraction_strategy(self):
        """Test Shopify extraction strategy."""
        strategy = get_extraction_strategy(SiteType.SHOPIFY)
        
        assert strategy["approach"] == "adaptive_ai"
        assert "variants" in strategy["priority_fields"]
        assert "variant_pricing" in strategy["special_handling"]
    
    def test_generic_extraction_strategy(self):
        """Test generic extraction strategy."""
        strategy = get_extraction_strategy(SiteType.GENERIC)
        
        assert strategy["approach"] == "ai_first"
        assert strategy["fallback"] == "structured_data"
        assert "adaptive_extraction" in strategy["special_handling"]


class TestNavigationStrategies:
    """Test navigation strategy configurations."""
    
    def test_amazon_navigation_strategy(self):
        """Test Amazon navigation strategy."""
        strategy = get_navigation_strategy(SiteType.AMAZON)
        
        assert "dismiss_location_popup" in strategy["initial_actions"]
        assert "accept_cookies" in strategy["initial_actions"]
        assert strategy["search_method"] == "main_search_box"
        assert strategy["anti_bot_handling"] == "slow_human_like"
    
    def test_ebay_navigation_strategy(self):
        """Test eBay navigation strategy."""
        strategy = get_navigation_strategy(SiteType.EBAY)
        
        assert "accept_cookies" in strategy["initial_actions"]
        assert strategy["search_method"] == "header_search"
        assert strategy["anti_bot_handling"] == "moderate_delays"
    
    def test_shopify_navigation_strategy(self):
        """Test Shopify navigation strategy."""
        strategy = get_navigation_strategy(SiteType.SHOPIFY)
        
        assert "handle_age_gate" in strategy["initial_actions"]
        assert strategy["search_method"] == "adaptive_search"
        assert strategy["anti_bot_handling"] == "respectful_timing"
    
    def test_generic_navigation_strategy(self):
        """Test generic navigation strategy."""
        strategy = get_navigation_strategy(SiteType.GENERIC)
        
        assert "dismiss_popups" in strategy["initial_actions"]
        assert strategy["search_method"] == "ai_guided"
        assert strategy["anti_bot_handling"] == "adaptive_behavior"


class TestSiteConfigValidation:
    """Test site configuration validation and completeness."""
    
    def test_all_site_configs_exist(self):
        """Test that all site types have configurations."""
        for site_type in SiteType:
            assert site_type in SITE_CONFIGS
    
    def test_site_config_completeness(self):
        """Test that all site configs have required fields."""
        required_fields = ["name", "site_type", "base_url"]
        
        for site_type, config in SITE_CONFIGS.items():
            for field in required_fields:
                assert hasattr(config, field)
                assert getattr(config, field) is not None
    
    def test_site_config_types(self):
        """Test that site config fields have correct types."""
        for site_type, config in SITE_CONFIGS.items():
            assert isinstance(config.name, str)
            assert isinstance(config.site_type, SiteType)
            assert isinstance(config.base_url, str)
            assert isinstance(config.delay_between_requests, int)
            assert isinstance(config.requires_cookie_consent, bool)
            assert isinstance(config.selectors, dict)
            assert isinstance(config.navigation_instructions, dict)
            assert isinstance(config.extraction_instructions, dict)
    
    def test_custom_site_config(self):
        """Test creating custom site configuration."""
        custom_config = SiteConfig(
            name="Test Store",
            site_type=SiteType.GENERIC,
            base_url="https://test-store.com",
            delay_between_requests=1,
            selectors={"title": ".product-title"},
            navigation_instructions={"test": "test instruction"},
            extraction_instructions={"test": "test extraction"}
        )
        
        assert custom_config.name == "Test Store"
        assert custom_config.site_type == SiteType.GENERIC
        assert custom_config.base_url == "https://test-store.com"
        assert custom_config.delay_between_requests == 1
        assert "title" in custom_config.selectors
        assert "test" in custom_config.navigation_instructions
        assert "test" in custom_config.extraction_instructions


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
