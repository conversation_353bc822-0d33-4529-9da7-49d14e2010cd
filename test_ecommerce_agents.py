#!/usr/bin/env python3
"""
Comprehensive test of the ecommerce scraping agents
Tests the full multi-agent CrewAI system with working Browserbase setup.
"""

import os
import sys
import asyncio
import json
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add the current directory to Python path so we can import ecommerce_scraper
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

console = Console()

def test_environment():
    """Test that all required environment variables are set."""
    console.print(Panel(
        "[bold blue]Environment Check[/bold blue]",
        title="🔍 Prerequisites",
        border_style="blue"
    ))
    
    required_vars = [
        "BROWSERBASE_API_KEY",
        "BROWSERBASE_PROJECT_ID", 
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            console.print(f"✅ {var}: {value[:20]}...")
        else:
            console.print(f"❌ {var}: NOT SET")
            missing_vars.append(var)
    
    if missing_vars:
        console.print(f"\n[red]Missing required environment variables: {', '.join(missing_vars)}[/red]")
        return False
    
    console.print("\n[green]✅ All environment variables are set![/green]")
    return True

def test_imports():
    """Test that all required modules can be imported."""
    console.print(Panel(
        "[bold blue]Import Test[/bold blue]",
        title="📦 Dependencies",
        border_style="blue"
    ))
    
    try:
        # Test core imports
        import crewai
        console.print("✅ CrewAI imported successfully")
        
        from ecommerce_scraper.main import EcommerceScraper, scrape_product
        console.print("✅ EcommerceScraper imported successfully")
        
        from ecommerce_scraper.config.sites import detect_site_type, get_site_config
        console.print("✅ Site configuration imported successfully")
        
        from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool
        console.print("✅ Stagehand tool imported successfully")
        
        console.print("\n[green]✅ All imports successful![/green]")
        return True
        
    except ImportError as e:
        console.print(f"\n[red]❌ Import error: {e}[/red]")
        return False

def test_single_product_scraping():
    """Test scraping a single product from a simple demo site."""
    console.print(Panel(
        "[bold blue]Single Product Scraping Test[/bold blue]",
        title="🛍️ Product Test",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.main import scrape_product
        
        # Use a simple demo ecommerce site for testing
        test_url = "https://demo.vercel.store/products/acme-cup"
        
        console.print(f"Testing with URL: {test_url}")
        console.print("⏳ This may take a few minutes as the AI agents work...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Scraping product...", total=None)
            
            result = scrape_product(
                product_url=test_url,
                site_type="generic",
                verbose=True
            )
            
            progress.update(task, description="✅ Scraping complete!")
            progress.stop_task(task)
        
        # Display results
        if result["success"]:
            console.print("\n[green]✅ Product scraping successful![/green]")
            
            # Create results table
            table = Table(title="Scraped Product Data")
            table.add_column("Field", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("URL", result["product_url"])
            table.add_row("Site Type", result["site_type"])
            table.add_row("Success", str(result["success"]))
            
            if result.get("data"):
                # Try to parse the data if it's a string
                data = result["data"]
                if isinstance(data, str):
                    try:
                        data = json.loads(data)
                    except:
                        pass
                
                table.add_row("Data Preview", str(data)[:200] + "..." if len(str(data)) > 200 else str(data))
            
            console.print(table)
            
            # Save results to file
            with open("test_results_single_product.json", "w") as f:
                json.dump(result, f, indent=2, default=str)
            console.print("\n📁 Results saved to: test_results_single_product.json")
            
            return True
        else:
            console.print(f"\n[red]❌ Product scraping failed: {result.get('error', 'Unknown error')}[/red]")
            return False
            
    except Exception as e:
        console.print(f"\n[red]❌ Test failed with exception: {str(e)}[/red]")
        import traceback
        console.print(f"Traceback:\n{traceback.format_exc()}")
        return False

def test_site_detection():
    """Test the site detection functionality."""
    console.print(Panel(
        "[bold blue]Site Detection Test[/bold blue]",
        title="🔍 Detection",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.config.sites import detect_site_type, get_site_config
        
        test_urls = [
            "https://www.amazon.com/dp/B08N5WRWNW",
            "https://www.ebay.com/itm/123456789",
            "https://demo.vercel.store/products/acme-cup",
            "https://shopify-store.myshopify.com/products/test"
        ]
        
        table = Table(title="Site Detection Results")
        table.add_column("URL", style="cyan")
        table.add_column("Detected Type", style="green")
        table.add_column("Config Name", style="yellow")
        
        for url in test_urls:
            try:
                site_type = detect_site_type(url)
                config = get_site_config(url)
                table.add_row(url, site_type.value, config.name)
            except Exception as e:
                table.add_row(url, "ERROR", str(e))
        
        console.print(table)
        console.print("\n[green]✅ Site detection test complete![/green]")
        return True
        
    except Exception as e:
        console.print(f"\n[red]❌ Site detection test failed: {str(e)}[/red]")
        return False

def test_stagehand_tool():
    """Test the Stagehand tool directly."""
    console.print(Panel(
        "[bold blue]Stagehand Tool Test[/bold blue]",
        title="🎭 Tool Test",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool
        
        console.print("🎭 Creating Stagehand tool...")
        tool = EcommerceStagehandTool()
        
        console.print("🌐 Testing navigation and extraction...")
        result = tool._run(
            instruction="Navigate to https://httpbin.org/html and extract the page title",
            url="https://httpbin.org/html",
            command_type="extract"
        )
        
        console.print(f"✅ Tool test result: {result}")
        
        # Cleanup
        tool.close()
        
        console.print("\n[green]✅ Stagehand tool test successful![/green]")
        return True
        
    except Exception as e:
        console.print(f"\n[red]❌ Stagehand tool test failed: {str(e)}[/red]")
        import traceback
        console.print(f"Traceback:\n{traceback.format_exc()}")
        return False

def main():
    """Run all tests."""
    console.print(Panel(
        "[bold yellow]Ecommerce Scraping Agents Test Suite[/bold yellow]\n"
        "Comprehensive testing of the multi-agent ecommerce scraping system",
        title="🧪 Test Suite",
        border_style="yellow"
    ))
    
    tests = [
        ("Environment Check", test_environment),
        ("Import Test", test_imports),
        ("Site Detection", test_site_detection),
        ("Stagehand Tool", test_stagehand_tool),
        ("Single Product Scraping", test_single_product_scraping),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*60}")
        console.print(f"Running: {test_name}")
        console.print('='*60)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            console.print(f"[red]❌ {test_name} failed with exception: {str(e)}[/red]")
            results[test_name] = False
    
    # Summary
    console.print(f"\n{'='*60}")
    console.print("TEST SUMMARY")
    console.print('='*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        console.print(f"{status} {test_name}")
    
    console.print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        console.print("\n🎉 [bold green]All tests passed! Your ecommerce scraping system is working correctly![/bold green]")
    else:
        console.print(f"\n⚠️ [bold yellow]{total - passed} test(s) failed. Please check the errors above.[/bold yellow]")

if __name__ == "__main__":
    main()
