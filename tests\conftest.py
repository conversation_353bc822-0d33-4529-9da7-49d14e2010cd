"""Pytest configuration and fixtures for the ecommerce scraper tests."""

import pytest
import os
from unittest.mock import Mock, patch
from ecommerce_scraper.config.settings import Settings


@pytest.fixture
def mock_settings():
    """Mock settings for testing without requiring real API keys."""
    with patch.dict(os.environ, {
        'BROWSERBASE_API_KEY': 'test_browserbase_key',
        'BROWSERBASE_PROJECT_ID': 'test_project_id',
        'OPENAI_API_KEY': 'test_openai_key',
        'STAGEHAND_MODEL_NAME': 'gpt-4o',
        'STAGEHAND_ENABLE_CACHING': 'true'
    }):
        yield Settings()


@pytest.fixture
def sample_product_data():
    """Sample product data for testing."""
    return {
        "title": "Test Product",
        "price": {
            "current": 29.99,
            "original": 39.99,
            "currency": "USD"
        },
        "availability": "IN_STOCK",
        "condition": "NEW",
        "brand": "Test Brand",
        "description": "A comprehensive test product with all features.",
        "images": [
            {
                "url": "https://example.com/image1.jpg",
                "alt_text": "Product main image",
                "is_primary": True
            },
            {
                "url": "https://example.com/image2.jpg",
                "alt_text": "Product secondary image",
                "is_primary": False
            }
        ],
        "variants": [
            {
                "name": "Color",
                "value": "Red",
                "price_modifier": 0.0
            },
            {
                "name": "Size",
                "value": "Large",
                "price_modifier": 5.0
            }
        ],
        "reviews": {
            "average_rating": 4.5,
            "total_count": 1250,
            "rating_distribution": {
                "5": 800,
                "4": 300,
                "3": 100,
                "2": 30,
                "1": 20
            }
        },
        "specifications": {
            "Weight": "2.5 lbs",
            "Dimensions": "10 x 8 x 3 inches",
            "Material": "Plastic",
            "Color": "Red"
        },
        "source_url": "https://example.com/product/test-item"
    }


@pytest.fixture
def sample_amazon_product_data():
    """Sample Amazon-specific product data."""
    return {
        "title": "Amazon Echo Dot (4th Gen)",
        "price": {
            "current": 49.99,
            "original": 59.99,
            "currency": "USD"
        },
        "availability": "IN_STOCK",
        "condition": "NEW",
        "brand": "Amazon",
        "description": "Smart speaker with Alexa voice control",
        "rating": 4.6,
        "review_count": 89543,
        "prime_eligible": True,
        "amazon_choice": True,
        "source_url": "https://www.amazon.com/dp/B08N5WRWNW"
    }


@pytest.fixture
def sample_ebay_product_data():
    """Sample eBay-specific product data."""
    return {
        "title": "Vintage Camera - Excellent Condition",
        "price": {
            "current": 125.00,
            "currency": "USD"
        },
        "availability": "IN_STOCK",
        "condition": "USED_EXCELLENT",
        "listing_type": "BUY_IT_NOW",
        "seller": {
            "name": "vintage_camera_seller",
            "rating": 99.2,
            "feedback_count": 1543
        },
        "shipping": {
            "cost": 12.50,
            "method": "Standard Shipping",
            "estimated_delivery": "3-5 business days"
        },
        "source_url": "https://www.ebay.com/itm/123456789"
    }


@pytest.fixture
def mock_stagehand_tool():
    """Mock Stagehand tool for testing without browser automation."""
    mock_tool = Mock()
    mock_tool._run.return_value = "Mock extraction result"
    mock_tool.close.return_value = None
    return mock_tool


@pytest.fixture
def mock_crew_result():
    """Mock CrewAI result for testing."""
    return {
        "success": True,
        "data": {
            "title": "Extracted Product",
            "price": 29.99,
            "availability": "IN_STOCK"
        }
    }


@pytest.fixture(scope="session")
def test_urls():
    """Test URLs for different site types."""
    return {
        "amazon": "https://www.amazon.com/dp/B08N5WRWNW",
        "ebay": "https://www.ebay.com/itm/123456789",
        "shopify": "https://store.myshopify.com/products/test-item",
        "generic": "https://example-store.com/product/test-item"
    }


# Skip tests that require real API keys if not available
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "requires_api_keys: mark test as requiring real API keys"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to skip tests requiring API keys."""
    skip_api_tests = pytest.mark.skip(reason="API keys not available")
    skip_integration = pytest.mark.skip(reason="Integration tests disabled")
    
    # Check if API keys are available
    has_browserbase = os.getenv("BROWSERBASE_API_KEY") and os.getenv("BROWSERBASE_PROJECT_ID")
    has_llm_key = os.getenv("OPENAI_API_KEY") or os.getenv("ANTHROPIC_API_KEY")
    
    for item in items:
        if "requires_api_keys" in item.keywords and not (has_browserbase and has_llm_key):
            item.add_marker(skip_api_tests)
        
        if "integration" in item.keywords and not config.getoption("--integration", default=False):
            item.add_marker(skip_integration)


def pytest_addoption(parser):
    """Add custom command line options."""
    parser.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="run integration tests"
    )
    parser.addoption(
        "--api-tests",
        action="store_true", 
        default=False,
        help="run tests that require API keys"
    )
