#!/usr/bin/env python3
"""
Simple key verification script
"""

import os
from dotenv import load_dotenv

load_dotenv()

print("Environment Variables Check:")
print("=" * 40)

api_key = os.getenv("BROWSERBASE_API_KEY")
project_id = os.getenv("BROWSERBASE_PROJECT_ID")
openai_key = os.getenv("OPENAI_API_KEY")

print(f"BROWSERBASE_API_KEY: {api_key[:20]}..." if api_key else "BROWSERBASE_API_KEY: NOT SET")
print(f"BROWSERBASE_PROJECT_ID: {project_id}")
print(f"OPENAI_API_KEY: {openai_key[:20]}..." if openai_key else "OPENAI_API_KEY: NOT SET")

print("\nKey Validation:")
print("=" * 40)

if api_key and api_key.startswith("bb_live_"):
    print("✅ Browserbase API key format looks correct")
else:
    print("❌ Browserbase API key format incorrect or missing")

if project_id and len(project_id) == 36:  # UUID length
    print("✅ Project ID format looks correct")
else:
    print("❌ Project ID format incorrect or missing")

if openai_key and openai_key.startswith("sk-"):
    print("✅ OpenAI API key format looks correct")
else:
    print("❌ OpenAI API key format incorrect or missing")
