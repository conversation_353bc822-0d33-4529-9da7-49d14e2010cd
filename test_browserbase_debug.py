#!/usr/bin/env python3
"""
Browserbase Session Debug Test
Detailed debugging of Browserbase session creation and management.
"""

import os
import asyncio
import requests
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
import time

# Load environment variables
load_dotenv()

console = Console()

def test_browserbase_api():
    """Test direct Browserbase API access."""
    api_key = os.getenv("BROWSERBASE_API_KEY")
    project_id = os.getenv("BROWSERBASE_PROJECT_ID")
    
    console.print(Panel(
        "[bold blue]Testing Direct Browserbase API[/bold blue]",
        title="🔍 API Debug Test",
        border_style="blue"
    ))
    
    # Test API key validation
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        # List sessions
        console.print("📡 Testing API connection...")
        response = requests.get(
            f"https://www.browserbase.com/v1/projects/{project_id}/sessions",
            headers=headers,
            timeout=10
        )
        
        console.print(f"Status Code: {response.status_code}")
        console.print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            sessions = response.json()
            console.print(f"✅ API connection successful!")
            console.print(f"Current sessions: {len(sessions.get('sessions', []))}")
            
            # Display sessions table
            if sessions.get('sessions'):
                table = Table(title="Active Sessions")
                table.add_column("Session ID", style="cyan")
                table.add_column("Status", style="green")
                table.add_column("Created", style="yellow")
                
                for session in sessions['sessions']:
                    table.add_row(
                        session.get('id', 'N/A'),
                        session.get('status', 'N/A'),
                        session.get('createdAt', 'N/A')
                    )
                console.print(table)
            else:
                console.print("📭 No active sessions found")
                
        else:
            console.print(f"❌ API Error: {response.status_code}")
            console.print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        console.print(f"❌ API request failed: {str(e)}")
        return False
    
    # Test session creation
    try:
        console.print("\n🚀 Testing session creation...")
        create_response = requests.post(
            f"https://www.browserbase.com/v1/projects/{project_id}/sessions",
            headers=headers,
            json={
                "browserSettings": {
                    "viewport": {"width": 1280, "height": 720}
                }
            },
            timeout=30
        )
        
        console.print(f"Create Status Code: {create_response.status_code}")
        
        if create_response.status_code == 201:
            session_data = create_response.json()
            session_id = session_data.get('id')
            console.print(f"✅ Session created successfully!")
            console.print(f"Session ID: {session_id}")
            console.print(f"Session Data: {session_data}")
            
            # Wait a moment and check if session appears in dashboard
            console.print("⏳ Waiting 5 seconds for session to appear in dashboard...")
            time.sleep(5)
            
            # Check session status
            status_response = requests.get(
                f"https://www.browserbase.com/v1/projects/{project_id}/sessions/{session_id}",
                headers=headers,
                timeout=10
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                console.print(f"Session Status: {status_data.get('status')}")
                console.print(f"Session Details: {status_data}")
            
            return session_id
        else:
            console.print(f"❌ Session creation failed: {create_response.status_code}")
            console.print(f"Response: {create_response.text}")
            return False
            
    except Exception as e:
        console.print(f"❌ Session creation failed: {str(e)}")
        return False

async def test_stagehand_with_session(session_id=None):
    """Test Stagehand with explicit session management."""
    console.print(Panel(
        "[bold blue]Testing Stagehand with Session Management[/bold blue]",
        title="🎭 Stagehand Test",
        border_style="blue"
    ))
    
    api_key = os.getenv("BROWSERBASE_API_KEY")
    project_id = os.getenv("BROWSERBASE_PROJECT_ID")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    try:
        from stagehand import Stagehand
        
        # Create Stagehand instance
        console.print("🎭 Creating Stagehand instance...")
        
        stagehand_config = {
            "api_key": api_key,
            "project_id": project_id,
            "model_api_key": openai_key,
            "model_name": "gpt-4o",
            "headless": True,
            "verbose": 2  # Increase verbosity
        }
        
        if session_id:
            console.print(f"🔗 Using existing session: {session_id}")
            stagehand_config["session_id"] = session_id
        
        stagehand = Stagehand(**stagehand_config)
        
        console.print("⚡ Initializing Stagehand...")
        await stagehand.init()
        
        console.print("✅ Stagehand initialized successfully!")
        
        # Test basic navigation
        console.print("🌐 Testing navigation...")
        await stagehand.page.goto("https://httpbin.org/html")
        
        console.print("📄 Testing extraction...")
        result = await stagehand.page.extract("Get the page title")
        
        console.print(f"✅ Extraction result: {result}")
        
        # Keep session alive for a moment
        console.print("⏳ Keeping session alive for 10 seconds...")
        await asyncio.sleep(10)
        
        console.print("🧹 Closing Stagehand...")
        await stagehand.close()
        
        return True
        
    except Exception as e:
        console.print(f"❌ Stagehand test failed: {str(e)}")
        import traceback
        console.print(f"Traceback:\n{traceback.format_exc()}")
        return False

async def main():
    """Main debug function."""
    console.print(Panel(
        "[bold yellow]Browserbase Session Debug Suite[/bold yellow]\n"
        "Comprehensive testing of Browserbase session management",
        title="🔧 Debug Suite",
        border_style="yellow"
    ))
    
    # Test 1: Direct API access
    session_id = test_browserbase_api()
    
    if session_id:
        console.print(f"\n✅ Session created via API: {session_id}")
        console.print("👀 Check your Browserbase dashboard now - you should see an active session!")
        
        # Test 2: Stagehand with existing session
        await test_stagehand_with_session(session_id)
    else:
        console.print("\n❌ API test failed, skipping Stagehand test")
        
        # Test 3: Stagehand without pre-created session
        console.print("\n🔄 Trying Stagehand without pre-created session...")
        await test_stagehand_with_session()

if __name__ == "__main__":
    asyncio.run(main())
