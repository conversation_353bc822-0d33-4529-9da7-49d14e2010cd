#!/usr/bin/env python3
import os
import requests
from dotenv import load_dotenv

load_dotenv()

api_key = os.getenv("BROWSERBASE_API_KEY")
project_id = os.getenv("BROWSERBASE_PROJECT_ID")

print(f"API Key: {api_key[:20]}..." if api_key else "No API key")
print(f"Project ID: {project_id}")

headers = {
    "X-BB-API-Key": api_key,
    "Content-Type": "application/json"
}

print("\nTesting session creation...")

try:
    response = requests.post(
        "https://api.browserbase.com/v1/sessions",
        headers=headers,
        json={"projectId": project_id},
        timeout=10
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 201:
        print("✅ SUCCESS! Session created!")
        data = response.json()
        print(f"Session ID: {data.get('id')}")
        print("👀 Check your Browserbase dashboard now!")
    else:
        print("❌ Failed to create session")
        
except Exception as e:
    print(f"Error: {e}")
