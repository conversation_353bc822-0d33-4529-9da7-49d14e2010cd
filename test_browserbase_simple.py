#!/usr/bin/env python3
"""
Simple Browserbase Session Test
Tests basic Browserbase session creation and management without CrewAI complexity.
"""

import os
import asyncio
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
import time

# Load environment variables
load_dotenv()

console = Console()

async def test_browserbase_session():
    """Test basic Browserbase session creation and management."""
    
    # Check environment variables
    api_key = os.getenv("BROWSERBASE_API_KEY")
    project_id = os.getenv("BROWSERBASE_PROJECT_ID")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    if not api_key or not project_id or not openai_key:
        console.print("❌ Missing required environment variables!", style="red")
        console.print(f"BROWSERBASE_API_KEY: {'✅' if api_key else '❌'}")
        console.print(f"BROWSERBASE_PROJECT_ID: {'✅' if project_id else '❌'}")
        console.print(f"OPENAI_API_KEY: {'✅' if openai_key else '❌'}")
        return False
    
    console.print("✅ All environment variables found!", style="green")
    
    try:
        # Import stagehand
        from stagehand import Stagehand
        console.print("✅ Stagehand imported successfully!", style="green")
        
        # Create Stagehand instance
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # Initialize Stagehand
            init_task = progress.add_task("Initializing Stagehand...", total=None)
            
            stagehand = Stagehand(
                api_key=api_key,
                project_id=project_id,
                model_api_key=openai_key,
                model_name="gpt-4o",
                headless=True,
                verbose=1
            )
            
            progress.update(init_task, description="Calling stagehand.init()...")
            await stagehand.init()
            
            progress.update(init_task, description="✅ Stagehand initialized!")
            progress.stop_task(init_task)
            
            # Test navigation
            nav_task = progress.add_task("Testing navigation...", total=None)
            
            test_url = "https://httpbin.org/html"
            progress.update(nav_task, description=f"Navigating to {test_url}...")
            
            await stagehand.page.goto(test_url)
            
            progress.update(nav_task, description="✅ Navigation successful!")
            progress.stop_task(nav_task)
            
            # Test extraction
            extract_task = progress.add_task("Testing extraction...", total=None)
            
            progress.update(extract_task, description="Extracting page title...")
            
            result = await stagehand.page.extract(
                instruction="Extract the page title and any visible text content"
            )
            
            progress.update(extract_task, description="✅ Extraction successful!")
            progress.stop_task(extract_task)
            
            # Cleanup
            cleanup_task = progress.add_task("Cleaning up...", total=None)
            await stagehand.close()
            progress.update(cleanup_task, description="✅ Cleanup complete!")
            progress.stop_task(cleanup_task)
        
        # Display results
        console.print("\n" + "="*60)
        console.print(Panel(
            f"[green]✅ Test Successful![/green]\n\n"
            f"[bold]URL:[/bold] {test_url}\n"
            f"[bold]Extraction Result:[/bold]\n{result}",
            title="Browserbase Test Results",
            border_style="green"
        ))
        
        return True
        
    except Exception as e:
        console.print(f"\n❌ Test failed with error: {str(e)}", style="red")
        console.print(f"Error type: {type(e).__name__}", style="red")
        import traceback
        console.print(f"Traceback:\n{traceback.format_exc()}", style="red")
        return False

async def main():
    """Main test function."""
    console.print(Panel(
        "[bold blue]Browserbase Session Test[/bold blue]\n"
        "Testing basic session creation and management",
        title="🧪 Simple Test Suite",
        border_style="blue"
    ))
    
    success = await test_browserbase_session()
    
    if success:
        console.print("\n🎉 All tests passed! Browserbase is working correctly.", style="green")
    else:
        console.print("\n💥 Tests failed. Please check the error messages above.", style="red")

if __name__ == "__main__":
    asyncio.run(main())
