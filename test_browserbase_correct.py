#!/usr/bin/env python3
"""
Corrected Browserbase Session Test
Using the correct API endpoint and authentication format.
"""

import os
import asyncio
import requests
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
import time

# Load environment variables
load_dotenv()

console = Console()

def test_browserbase_api_correct():
    """Test Browserbase API with correct endpoint and headers."""
    api_key = os.getenv("BROWSERBASE_API_KEY")
    project_id = os.getenv("BROWSERBASE_PROJECT_ID")
    
    console.print(Panel(
        "[bold blue]Testing Corrected Browserbase API[/bold blue]",
        title="🔍 Corrected API Test",
        border_style="blue"
    ))
    
    # Correct headers format
    headers = {
        "X-BB-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    try:
        # Test session creation with correct endpoint
        console.print("🚀 Creating session with correct API...")
        
        create_response = requests.post(
            "https://api.browserbase.com/v1/sessions",  # Correct endpoint
            headers=headers,
            json={"projectId": project_id},
            timeout=30
        )
        
        console.print(f"Status Code: {create_response.status_code}")
        console.print(f"Response Headers: {dict(create_response.headers)}")
        
        if create_response.status_code == 201:
            session_data = create_response.json()
            session_id = session_data.get('id')
            console.print(f"✅ Session created successfully!")
            console.print(f"Session ID: {session_id}")
            console.print(f"Status: {session_data.get('status')}")
            console.print(f"Connect URL: {session_data.get('connectUrl')}")
            
            console.print("\n🎉 SUCCESS! Session is now active in Browserbase dashboard!")
            console.print("👀 Check your dashboard - you should see the session running.")
            
            # Keep session alive for a moment so you can see it in dashboard
            console.print("⏳ Keeping session alive for 30 seconds...")
            time.sleep(30)
            
            return session_id
        else:
            console.print(f"❌ Session creation failed: {create_response.status_code}")
            console.print(f"Response: {create_response.text}")
            return False
            
    except Exception as e:
        console.print(f"❌ API request failed: {str(e)}")
        return False

async def test_stagehand_with_working_session():
    """Test Stagehand now that we know the API works."""
    console.print(Panel(
        "[bold green]Testing Stagehand with Working API[/bold green]",
        title="🎭 Stagehand Test",
        border_style="green"
    ))
    
    api_key = os.getenv("BROWSERBASE_API_KEY")
    project_id = os.getenv("BROWSERBASE_PROJECT_ID")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    try:
        from stagehand import Stagehand
        
        console.print("🎭 Creating Stagehand instance...")
        
        stagehand = Stagehand(
            api_key=api_key,
            project_id=project_id,
            model_api_key=openai_key,
            model_name="gpt-4o",
            headless=True,
            verbose=2
        )
        
        console.print("⚡ Initializing Stagehand...")
        await stagehand.init()
        
        console.print("✅ Stagehand initialized successfully!")
        console.print("👀 Check your Browserbase dashboard - you should see a new session!")
        
        # Test navigation
        console.print("🌐 Testing navigation to simple page...")
        await stagehand.page.goto("https://httpbin.org/html")
        
        console.print("📄 Testing extraction...")
        result = await stagehand.page.extract("Get the page title and main heading")
        
        console.print(f"✅ Extraction successful!")
        console.print(f"Result: {result}")
        
        # Keep session alive
        console.print("⏳ Keeping session alive for 15 seconds...")
        await asyncio.sleep(15)
        
        console.print("🧹 Closing Stagehand...")
        await stagehand.close()
        
        return True
        
    except Exception as e:
        console.print(f"❌ Stagehand test failed: {str(e)}")
        import traceback
        console.print(f"Traceback:\n{traceback.format_exc()}")
        return False

def main():
    """Main test function."""
    console.print(Panel(
        "[bold yellow]Browserbase Session Debug Suite - Corrected[/bold yellow]\n"
        "Testing with correct API endpoint and authentication",
        title="🔧 Corrected Debug Suite",
        border_style="yellow"
    ))
    
    # Test 1: Direct API with correct format
    session_id = test_browserbase_api_correct()
    
    if session_id:
        console.print(f"\n✅ Direct API test successful!")
        console.print("🎉 Your Browserbase setup is working correctly!")
        
        # Test 2: Stagehand
        console.print("\n🔄 Now testing Stagehand...")
        asyncio.run(test_stagehand_with_working_session())
    else:
        console.print("\n❌ Direct API test failed")
        console.print("Please check your API keys and project ID")

if __name__ == "__main__":
    main()
