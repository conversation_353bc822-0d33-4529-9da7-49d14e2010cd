"""
Minimal StagehandTool implementation that bypasses CrewAI dependencies.
This is a workaround for the Visual C++ build tools issue.
"""

import os
from typing import Dict, Any, Optional
from stagehand import Stagehand
from stagehand.schemas import AvailableModel


class MinimalStagehandTool:
    """Minimal implementation of StagehandTool functionality."""
    
    def __init__(self):
        """Initialize the tool."""
        self._stagehand = None
        self._current_url = None
    
    def _get_stagehand(self) -> Stagehand:
        """Get or create Stagehand instance."""
        if self._stagehand is None:
            # Determine which model to use
            if os.getenv("OPENAI_API_KEY"):
                model_name = AvailableModel.GPT_4O
                model_api_key = os.getenv("OPENAI_API_KEY")
            else:
                model_name = AvailableModel.CLAUDE_3_5_SONNET
                model_api_key = os.getenv("ANTHROPIC_API_KEY")
            
            self._stagehand = Stagehand(
                api_key=os.getenv("BROWSERBASE_API_KEY"),
                project_id=os.getenv("BROWSERBASE_PROJECT_ID"),
                model_api_key=model_api_key,
                model_name=model_name,
                headless=True,
                verbose=1
            )
        
        return self._stagehand
    
    async def run(self, instruction: str, url: Optional[str] = None, 
                  command_type: str = "extract", **kwargs) -> str:
        """Run a Stagehand command."""
        try:
            stagehand = self._get_stagehand()
            
            # Initialize if not already done
            if stagehand.page is None:
                await stagehand.init()
            
            # Navigate to URL if provided and different from current
            if url and url != self._current_url:
                stagehand.page.goto(url)
                self._current_url = url
                # Wait for page to load
                import time
                time.sleep(2)
            
            # Execute command based on type
            if command_type == "act":
                result = stagehand.page.act(instruction)
                return f"Action completed: {result}"
                
            elif command_type == "extract":
                result = stagehand.page.extract(instruction)
                
                # Try to format as JSON if possible
                try:
                    import json
                    if isinstance(result, dict):
                        return json.dumps(result, indent=2)
                    else:
                        return str(result)
                except:
                    return str(result)
                    
            else:
                return f"Unknown command type: {command_type}"
                
        except Exception as e:
            return f"Error: {str(e)}"
    
    def close(self):
        """Close the Stagehand instance."""
        if self._stagehand:
            try:
                # Note: close() might be async in some versions
                self._stagehand.close()
            except:
                pass
            self._stagehand = None
