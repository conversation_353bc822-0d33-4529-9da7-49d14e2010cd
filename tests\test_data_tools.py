"""Tests for data validation and processing tools."""

import pytest
from decimal import Decimal
from ecommerce_scraper.tools.data_tools import ProductDataValidator, PriceExtractor, ImageExtractor
from ecommerce_scraper.schemas.product import Product, ProductPrice, ProductAvailability


class TestPriceExtractor:
    """Test the PriceExtractor tool."""
    
    def test_extract_simple_price(self):
        """Test extracting simple price formats."""
        extractor = PriceExtractor()
        
        # Test various price formats
        test_cases = [
            ("$29.99", 29.99),
            ("£15.50", 15.50),
            ("€45.00", 45.00),
            ("¥1000", 1000.0),
            ("$1,299.99", 1299.99),
            ("19.95", 19.95),
        ]
        
        for price_text, expected in test_cases:
            result = extractor._run(price_text=price_text)
            assert float(result) == expected
    
    def test_extract_price_with_currency(self):
        """Test extracting prices with currency detection."""
        extractor = PriceExtractor()
        
        test_cases = [
            ("$29.99 USD", ("29.99", "USD")),
            ("£15.50 GBP", ("15.50", "GBP")),
            ("€45.00 EUR", ("45.00", "EUR")),
        ]
        
        for price_text, (expected_amount, expected_currency) in test_cases:
            result = extractor._run(price_text=price_text, extract_currency=True)
            assert expected_amount in result
            assert expected_currency in result
    
    def test_extract_invalid_price(self):
        """Test handling invalid price formats."""
        extractor = PriceExtractor()
        
        invalid_prices = [
            "Not a price",
            "",
            "Free",
            "Contact for price",
        ]
        
        for invalid_price in invalid_prices:
            result = extractor._run(price_text=invalid_price)
            assert result == "0.00" or "error" in result.lower()


class TestImageExtractor:
    """Test the ImageExtractor tool."""
    
    def test_resolve_relative_urls(self):
        """Test resolving relative image URLs."""
        extractor = ImageExtractor()
        
        test_cases = [
            ("/images/product.jpg", "https://example.com", "https://example.com/images/product.jpg"),
            ("../images/product.jpg", "https://example.com/products/", "https://example.com/images/product.jpg"),
            ("product.jpg", "https://example.com/products/", "https://example.com/products/product.jpg"),
            ("https://cdn.example.com/image.jpg", "https://example.com", "https://cdn.example.com/image.jpg"),
        ]
        
        for relative_url, base_url, expected in test_cases:
            result = extractor._run(image_urls=relative_url, base_url=base_url)
            assert expected in result
    
    def test_extract_multiple_images(self):
        """Test extracting multiple image URLs."""
        extractor = ImageExtractor()
        
        image_urls = [
            "/images/product1.jpg",
            "/images/product2.jpg",
            "https://cdn.example.com/product3.jpg"
        ]
        
        result = extractor._run(
            image_urls=",".join(image_urls),
            base_url="https://example.com"
        )
        
        assert "https://example.com/images/product1.jpg" in result
        assert "https://example.com/images/product2.jpg" in result
        assert "https://cdn.example.com/product3.jpg" in result


class TestProductDataValidator:
    """Test the ProductDataValidator tool."""
    
    def test_validate_complete_product(self):
        """Test validating a complete product data structure."""
        validator = ProductDataValidator()
        
        product_data = {
            "title": "Test Product",
            "price": {
                "current": 29.99,
                "currency": "USD"
            },
            "availability": "IN_STOCK",
            "condition": "NEW",
            "brand": "Test Brand",
            "description": "A test product description",
            "images": [
                {
                    "url": "https://example.com/image1.jpg",
                    "alt_text": "Product image",
                    "is_primary": True
                }
            ]
        }
        
        result = validator._run(product_data=str(product_data))
        assert "valid" in result.lower() or "success" in result.lower()
    
    def test_validate_minimal_product(self):
        """Test validating minimal required product data."""
        validator = ProductDataValidator()
        
        minimal_data = {
            "title": "Minimal Product",
            "availability": "IN_STOCK"
        }
        
        result = validator._run(product_data=str(minimal_data))
        # Should handle missing fields gracefully
        assert isinstance(result, str)
    
    def test_clean_product_title(self):
        """Test cleaning product titles."""
        validator = ProductDataValidator()
        
        dirty_titles = [
            "  Product Name  ",
            "Product Name!!!",
            "PRODUCT NAME",
            "Product Name - Brand Name",
        ]
        
        for dirty_title in dirty_titles:
            data = {"title": dirty_title}
            result = validator._run(product_data=str(data))
            # Should contain cleaned version
            assert isinstance(result, str)
    
    def test_validate_price_formats(self):
        """Test validating different price formats."""
        validator = ProductDataValidator()
        
        price_formats = [
            {"price": "$29.99"},
            {"price": {"current": 29.99, "currency": "USD"}},
            {"price": "29.99"},
            {"price": 29.99},
        ]
        
        for price_format in price_formats:
            data = {"title": "Test", **price_format}
            result = validator._run(product_data=str(data))
            assert isinstance(result, str)


class TestProductSchema:
    """Test the Product schema validation."""
    
    def test_create_valid_product(self):
        """Test creating a valid product instance."""
        product_data = {
            "title": "Test Product",
            "availability": ProductAvailability.IN_STOCK,
            "source_url": "https://example.com/product"
        }
        
        product = Product(**product_data)
        assert product.title == "Test Product"
        assert product.availability == ProductAvailability.IN_STOCK
        assert product.source_url == "https://example.com/product"
    
    def test_product_with_price(self):
        """Test creating a product with price information."""
        price_data = {
            "current": Decimal("29.99"),
            "currency": "USD"
        }
        
        product_data = {
            "title": "Test Product",
            "price": ProductPrice(**price_data),
            "availability": ProductAvailability.IN_STOCK,
            "source_url": "https://example.com/product"
        }
        
        product = Product(**product_data)
        assert product.price.current == Decimal("29.99")
        assert product.price.currency == "USD"
    
    def test_product_validation_errors(self):
        """Test product validation with invalid data."""
        with pytest.raises(ValueError):
            # Missing required title
            Product(availability=ProductAvailability.IN_STOCK)
        
        with pytest.raises(ValueError):
            # Invalid availability
            Product(title="Test", availability="INVALID_STATUS")
    
    def test_product_serialization(self):
        """Test product serialization to dict."""
        product = Product(
            title="Test Product",
            availability=ProductAvailability.IN_STOCK,
            source_url="https://example.com/product"
        )
        
        product_dict = product.model_dump()
        assert product_dict["title"] == "Test Product"
        assert product_dict["availability"] == "IN_STOCK"
        assert product_dict["source_url"] == "https://example.com/product"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
